2025-06-21 22:40:10,487 INFO ipython === bench console session ===
2025-06-21 22:40:10,487 INFO ipython frappe.get_doc("Energy Point Settings")
2025-06-21 22:40:10,487 INFO ipython === session end ===
2025-06-21 22:49:14,291 INFO ipython === bench console session ===
2025-06-21 22:49:14,291 INFO ipython frappe.db.sql("SELECT COUNT(*) FROM tabUser")
2025-06-21 22:49:14,291 INFO ipython frappe.get_meta("User").fields[:3]
2025-06-21 22:49:14,291 INFO ipython frappe.get_all("DocType", limit=5)
2025-06-21 22:49:14,291 INFO ipython === session end ===
2025-06-21 22:51:27,939 INFO ipython === bench console session ===
2025-06-21 22:51:27,939 INFO ipython frappe.get_installed_apps()
2025-06-21 22:51:27,939 INFO ipython # Test ERPNext
2025-06-21 22:51:27,939 INFO ipython frappe.get_meta("Customer").name
2025-06-21 22:51:27,939 INFO ipython # Test HRMS
2025-06-21 22:51:27,939 INFO ipython frappe.get_meta("Employee").name
2025-06-21 22:51:27,939 INFO ipython # Test Tourism app
2025-06-21 22:51:27,939 INFO ipython frappe.get_all("DocType", filters={"module": "Tourism"}, limit=3)
2025-06-21 22:51:27,939 INFO ipython # Test Payments app
2025-06-21 22:51:27,939 INFO ipython frappe.get_all("DocType", filters={"module": "Payments"}, limit=2)
2025-06-21 22:51:27,940 INFO ipython === session end ===
2025-06-21 22:52:50,754 INFO ipython === bench console session ===
2025-06-21 22:52:50,754 INFO ipython # Test the Energy Point Settings DocType that was causing the error
2025-06-21 22:52:50,754 INFO ipython energy_settings = frappe.get_doc("Energy Point Settings")
2025-06-21 22:52:50,754 INFO ipython print(f"DocType: {energy_settings.doctype}")
2025-06-21 22:52:50,754 INFO ipython print(f"Name: {energy_settings.name}")
2025-06-21 22:52:50,754 INFO ipython print(f"Enabled: {energy_settings.enabled}")
2025-06-21 22:52:50,755 INFO ipython # Test the specific function that was failing
2025-06-21 22:52:50,755 INFO ipython from frappe.social.doctype.energy_point_settings.energy_point_settings import is_energy_point_enabled
2025-06-21 22:52:50,755 INFO ipython result = is_energy_point_enabled()
2025-06-21 22:52:50,755 INFO ipython print(f"Energy points enabled: {result}")
2025-06-21 22:52:50,755 INFO ipython # Test the exact database query that was failing in the original error
2025-06-21 22:52:50,755 INFO ipython result = frappe.db.get_single_value("Energy Point Settings", "enabled", True)
2025-06-21 22:52:50,755 INFO ipython print(f"Database query result: {result}")
2025-06-21 22:52:50,755 INFO ipython === session end ===
