{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/job-bench/safari-tour-nextjs/src/lib/frappe-api.ts"], "sourcesContent": ["// Frappe API client to replace Supabase integration\nimport { QueryClient } from '@tanstack/react-query'\n\n// Base API configuration\nconst API_BASE_URL = process.env.NEXT_PUBLIC_FRAPPE_URL || 'http://localhost:8000'\nconst API_PREFIX = '/api/method/tourism'\n\n// Types based on our Frappe doctypes\nexport interface TourProfile {\n  name: string\n  user: string\n  username: string\n  display_name?: string\n  avatar_url?: string\n  bio?: string\n  is_seller: boolean\n  is_tour_guide: boolean\n  role: 'Tourist' | 'Tour Guide' | 'Seller' | 'Admin'\n  created_at: string\n  updated_at: string\n}\n\nexport interface TourPost {\n  name: string\n  tour_profile: string\n  tour: string\n  caption: string\n  image_url: string\n  location?: string\n  likes_count: number\n  comments_count: number\n  created_at: string\n  updated_at: string\n}\n\nexport interface TourProduct {\n  name: string\n  name_field: string\n  description: string\n  category: string\n  image_url: string\n  seller_id: string\n  in_stock: boolean\n  price: number\n  price_type?: string\n  price_min?: number\n  price_max?: number\n  discount_percentage?: number\n  promotion_text?: string\n  promotion_expires_at?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface TourReview {\n  name: string\n  tour_profile: string\n  tour: string\n  title: string\n  content: string\n  rating: number\n  helpful: boolean\n  likes_count: number\n  comments_count: number\n  created_at: string\n  updated_at: string\n}\n\n// API client class\nclass FrappeAPI {\n  private baseURL: string\n  private headers: HeadersInit\n\n  constructor() {\n    this.baseURL = API_BASE_URL + API_PREFIX\n    this.headers = {\n      'Content-Type': 'application/json',\n      'Accept': 'application/json',\n    }\n  }\n\n  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {\n    const url = `${this.baseURL}${endpoint}`\n    \n    const response = await fetch(url, {\n      ...options,\n      headers: {\n        ...this.headers,\n        ...options.headers,\n      },\n      credentials: 'include', // Important for Frappe session management\n    })\n\n    if (!response.ok) {\n      throw new Error(`API Error: ${response.status} ${response.statusText}`)\n    }\n\n    const data = await response.json()\n    return data.message || data\n  }\n\n  // Auth methods\n  async login(email: string, password: string) {\n    return this.request('/auth/login', {\n      method: 'POST',\n      body: JSON.stringify({ usr: email, pwd: password }),\n    })\n  }\n\n  async logout() {\n    return this.request('/auth/logout', { method: 'POST' })\n  }\n\n  async getCurrentUser() {\n    return this.request('/auth/user')\n  }\n\n  // Profile methods\n  async getProfile(username?: string): Promise<TourProfile> {\n    const endpoint = username ? `/profile/${username}` : '/profile'\n    return this.request(endpoint)\n  }\n\n  async createProfile(data: Partial<TourProfile>): Promise<TourProfile> {\n    return this.request('/profile', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    })\n  }\n\n  async updateProfile(data: Partial<TourProfile>): Promise<TourProfile> {\n    return this.request('/profile', {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    })\n  }\n\n  // Posts methods\n  async getPosts(limit = 20, offset = 0): Promise<TourPost[]> {\n    return this.request(`/posts?limit=${limit}&offset=${offset}`)\n  }\n\n  async createPost(data: Partial<TourPost>): Promise<TourPost> {\n    return this.request('/posts', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    })\n  }\n\n  async likePost(postId: string): Promise<void> {\n    return this.request(`/posts/${postId}/like`, { method: 'POST' })\n  }\n\n  async unlikePost(postId: string): Promise<void> {\n    return this.request(`/posts/${postId}/unlike`, { method: 'POST' })\n  }\n\n  // Products methods\n  async getProducts(category?: string): Promise<TourProduct[]> {\n    const endpoint = category ? `/products?category=${category}` : '/products'\n    return this.request(endpoint)\n  }\n\n  async getProduct(id: string): Promise<TourProduct> {\n    return this.request(`/products/${id}`)\n  }\n\n  async createProduct(data: Partial<TourProduct>): Promise<TourProduct> {\n    return this.request('/products', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    })\n  }\n\n  // Reviews methods\n  async getReviews(tour?: string): Promise<TourReview[]> {\n    const endpoint = tour ? `/reviews?tour=${tour}` : '/reviews'\n    return this.request(endpoint)\n  }\n\n  async createReview(data: Partial<TourReview>): Promise<TourReview> {\n    return this.request('/reviews', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    })\n  }\n\n  // File upload method\n  async uploadFile(file: File): Promise<{ file_url: string }> {\n    const formData = new FormData()\n    formData.append('file', file)\n\n    const response = await fetch(`${API_BASE_URL}/api/method/upload_file`, {\n      method: 'POST',\n      body: formData,\n      credentials: 'include',\n    })\n\n    if (!response.ok) {\n      throw new Error(`Upload failed: ${response.status} ${response.statusText}`)\n    }\n\n    const data = await response.json()\n    return data.message\n  }\n}\n\n// Export singleton instance\nexport const frappeAPI = new FrappeAPI()\n\n// Query client for React Query\nexport const queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      staleTime: 5 * 60 * 1000, // 5 minutes\n      retry: 1,\n    },\n  },\n})\n"], "names": [], "mappings": "AAAA,oDAAoD;;;;;AAI/B;AAHrB;;AAEA,yBAAyB;AACzB,MAAM,eAAe,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI;AAC3D,MAAM,aAAa;AA+DnB,mBAAmB;AACnB,MAAM;IACI,QAAe;IACf,QAAoB;IAE5B,aAAc;QACZ,IAAI,CAAC,OAAO,GAAG,eAAe;QAC9B,IAAI,CAAC,OAAO,GAAG;YACb,gBAAgB;YAChB,UAAU;QACZ;IACF;IAEA,MAAc,QAAW,QAAgB,EAAE,UAAuB,CAAC,CAAC,EAAc;QAChF,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;QAExC,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,GAAG,OAAO;YACV,SAAS;gBACP,GAAG,IAAI,CAAC,OAAO;gBACf,GAAG,QAAQ,OAAO;YACpB;YACA,aAAa;QACf;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QACxE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,OAAO,IAAI;IACzB;IAEA,eAAe;IACf,MAAM,MAAM,KAAa,EAAE,QAAgB,EAAE;QAC3C,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe;YACjC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE,KAAK;gBAAO,KAAK;YAAS;QACnD;IACF;IAEA,MAAM,SAAS;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,gBAAgB;YAAE,QAAQ;QAAO;IACvD;IAEA,MAAM,iBAAiB;QACrB,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB;IAEA,kBAAkB;IAClB,MAAM,WAAW,QAAiB,EAAwB;QACxD,MAAM,WAAW,WAAW,CAAC,SAAS,EAAE,UAAU,GAAG;QACrD,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB;IAEA,MAAM,cAAc,IAA0B,EAAwB;QACpE,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY;YAC9B,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cAAc,IAA0B,EAAwB;QACpE,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY;YAC9B,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,gBAAgB;IAChB,MAAM,SAAS,QAAQ,EAAE,EAAE,SAAS,CAAC,EAAuB;QAC1D,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,MAAM,QAAQ,EAAE,QAAQ;IAC9D;IAEA,MAAM,WAAW,IAAuB,EAAqB;QAC3D,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU;YAC5B,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,SAAS,MAAc,EAAiB;QAC5C,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,OAAO,KAAK,CAAC,EAAE;YAAE,QAAQ;QAAO;IAChE;IAEA,MAAM,WAAW,MAAc,EAAiB;QAC9C,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,OAAO,OAAO,CAAC,EAAE;YAAE,QAAQ;QAAO;IAClE;IAEA,mBAAmB;IACnB,MAAM,YAAY,QAAiB,EAA0B;QAC3D,MAAM,WAAW,WAAW,CAAC,mBAAmB,EAAE,UAAU,GAAG;QAC/D,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB;IAEA,MAAM,WAAW,EAAU,EAAwB;QACjD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,IAAI;IACvC;IAEA,MAAM,cAAc,IAA0B,EAAwB;QACpE,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa;YAC/B,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,kBAAkB;IAClB,MAAM,WAAW,IAAa,EAAyB;QACrD,MAAM,WAAW,OAAO,CAAC,cAAc,EAAE,MAAM,GAAG;QAClD,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB;IAEA,MAAM,aAAa,IAAyB,EAAuB;QACjE,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY;YAC9B,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,qBAAqB;IACrB,MAAM,WAAW,IAAU,EAAiC;QAC1D,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QAExB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,uBAAuB,CAAC,EAAE;YACrE,QAAQ;YACR,MAAM;YACN,aAAa;QACf;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QAC5E;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,OAAO;IACrB;AACF;AAGO,MAAM,YAAY,IAAI;AAGtB,MAAM,cAAc,IAAI,gLAAA,CAAA,cAAW,CAAC;IACzC,gBAAgB;QACd,SAAS;YACP,WAAW,IAAI,KAAK;YACpB,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/job-bench/safari-tour-nextjs/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useEffect, useState } from 'react'\nimport { frappeAPI, TourProfile } from '@/lib/frappe-api'\n\ninterface User {\n  email: string\n  name: string\n  full_name?: string\n  user_image?: string\n}\n\ninterface AuthContextType {\n  user: User | null\n  profile: TourProfile | null\n  loading: boolean\n  signIn: (email: string, password: string) => Promise<void>\n  signOut: () => Promise<void>\n  signUp: (email: string, password: string, fullName: string) => Promise<void>\n  updateProfile: (data: Partial<TourProfile>) => Promise<void>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [profile, setProfile] = useState<TourProfile | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    // Check if user is already logged in\n    checkUser()\n  }, [])\n\n  const checkUser = async () => {\n    try {\n      const userData = await frappeAPI.getCurrentUser()\n      if (userData && userData.name !== 'Guest') {\n        setUser(userData)\n        // Get user profile\n        try {\n          const profileData = await frappeAPI.getProfile()\n          setProfile(profileData)\n        } catch (error) {\n          console.log('No profile found for user')\n        }\n      }\n    } catch (error) {\n      console.log('User not authenticated')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signIn = async (email: string, password: string) => {\n    try {\n      setLoading(true)\n      await frappeAPI.login(email, password)\n      await checkUser()\n    } catch (error) {\n      setLoading(false)\n      throw error\n    }\n  }\n\n  const signOut = async () => {\n    try {\n      await frappeAPI.logout()\n      setUser(null)\n      setProfile(null)\n    } catch (error) {\n      console.error('Error signing out:', error)\n    }\n  }\n\n  const signUp = async (email: string, password: string, fullName: string) => {\n    // Note: Frappe user creation typically requires admin privileges\n    // This would need to be implemented as a custom API endpoint\n    throw new Error('User registration not implemented. Please contact administrator.')\n  }\n\n  const updateProfile = async (data: Partial<TourProfile>) => {\n    try {\n      const updatedProfile = await frappeAPI.updateProfile(data)\n      setProfile(updatedProfile)\n    } catch (error) {\n      throw error\n    }\n  }\n\n  const value = {\n    user,\n    profile,\n    loading,\n    signIn,\n    signOut,\n    signUp,\n    updateProfile,\n  }\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAsBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,qCAAqC;YACrC;QACF;iCAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,8HAAA,CAAA,YAAS,CAAC,cAAc;YAC/C,IAAI,YAAY,SAAS,IAAI,KAAK,SAAS;gBACzC,QAAQ;gBACR,mBAAmB;gBACnB,IAAI;oBACF,MAAM,cAAc,MAAM,8HAAA,CAAA,YAAS,CAAC,UAAU;oBAC9C,WAAW;gBACb,EAAE,OAAO,OAAO;oBACd,QAAQ,GAAG,CAAC;gBACd;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,IAAI;YACF,WAAW;YACX,MAAM,8HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,OAAO;YAC7B,MAAM;QACR,EAAE,OAAO,OAAO;YACd,WAAW;YACX,MAAM;QACR;IACF;IAEA,MAAM,UAAU;QACd,IAAI;YACF,MAAM,8HAAA,CAAA,YAAS,CAAC,MAAM;YACtB,QAAQ;YACR,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,MAAM,SAAS,OAAO,OAAe,UAAkB;QACrD,iEAAiE;QACjE,6DAA6D;QAC7D,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,MAAM,iBAAiB,MAAM,8HAAA,CAAA,YAAS,CAAC,aAAa,CAAC;YACrD,WAAW;QACb,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;GA7EgB;KAAA;AA+ET,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 274, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/job-bench/safari-tour-nextjs/src/contexts/CartContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useState, ReactNode } from \"react\";\n\nexport interface CartItem {\n  id: string;\n  name: string;\n  price: number;\n  quantity: number;\n  image: string;\n}\n\ninterface CartContextType {\n  cartItems: CartItem[];\n  addToCart: (item: Omit<CartItem, 'quantity'>) => void;\n  removeFromCart: (id: string) => void;\n  updateQuantity: (id: string, quantity: number) => void;\n  clearCart: () => void;\n  getTotalItems: () => number;\n  getTotalPrice: () => number;\n}\n\nconst CartContext = createContext<CartContextType | undefined>(undefined);\n\nexport const CartProvider = ({ children }: { children: ReactNode }) => {\n  const [cartItems, setCartItems] = useState<CartItem[]>([]);\n\n  const addToCart = (item: Omit<CartItem, 'quantity'>) => {\n    setCartItems(prev => {\n      const existingItem = prev.find(cartItem => cartItem.id === item.id);\n      if (existingItem) {\n        return prev.map(cartItem =>\n          cartItem.id === item.id\n            ? { ...cartItem, quantity: cartItem.quantity + 1 }\n            : cartItem\n        );\n      }\n      return [...prev, { ...item, quantity: 1 }];\n    });\n  };\n\n  const removeFromCart = (id: string) => {\n    setCartItems(prev => prev.filter(item => item.id !== id));\n  };\n\n  const updateQuantity = (id: string, quantity: number) => {\n    if (quantity <= 0) {\n      removeFromCart(id);\n      return;\n    }\n    setCartItems(prev =>\n      prev.map(item =>\n        item.id === id ? { ...item, quantity } : item\n      )\n    );\n  };\n\n  const clearCart = () => {\n    setCartItems([]);\n  };\n\n  const getTotalItems = () => {\n    return cartItems.reduce((total, item) => total + item.quantity, 0);\n  };\n\n  const getTotalPrice = () => {\n    return cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);\n  };\n\n  return (\n    <CartContext.Provider value={{\n      cartItems,\n      addToCart,\n      removeFromCart,\n      updateQuantity,\n      clearCart,\n      getTotalItems,\n      getTotalPrice\n    }}>\n      {children}\n    </CartContext.Provider>\n  );\n};\n\nexport const useCart = () => {\n  const context = useContext(CartContext);\n  if (!context) {\n    throw new Error('useCart must be used within a CartProvider');\n  }\n  return context;\n};\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAsBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,eAAe,CAAC,EAAE,QAAQ,EAA2B;;IAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAEzD,MAAM,YAAY,CAAC;QACjB,aAAa,CAAA;YACX,MAAM,eAAe,KAAK,IAAI,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK,KAAK,EAAE;YAClE,IAAI,cAAc;gBAChB,OAAO,KAAK,GAAG,CAAC,CAAA,WACd,SAAS,EAAE,KAAK,KAAK,EAAE,GACnB;wBAAE,GAAG,QAAQ;wBAAE,UAAU,SAAS,QAAQ,GAAG;oBAAE,IAC/C;YAER;YACA,OAAO;mBAAI;gBAAM;oBAAE,GAAG,IAAI;oBAAE,UAAU;gBAAE;aAAE;QAC5C;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,aAAa,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IACvD;IAEA,MAAM,iBAAiB,CAAC,IAAY;QAClC,IAAI,YAAY,GAAG;YACjB,eAAe;YACf;QACF;QACA,aAAa,CAAA,OACX,KAAK,GAAG,CAAC,CAAA,OACP,KAAK,EAAE,KAAK,KAAK;oBAAE,GAAG,IAAI;oBAAE;gBAAS,IAAI;IAG/C;IAEA,MAAM,YAAY;QAChB,aAAa,EAAE;IACjB;IAEA,MAAM,gBAAgB;QACpB,OAAO,UAAU,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,KAAK,QAAQ,EAAE;IAClE;IAEA,MAAM,gBAAgB;QACpB,OAAO,UAAU,MAAM,CAAC,CAAC,OAAO,OAAS,QAAS,KAAK,KAAK,GAAG,KAAK,QAAQ,EAAG;IACjF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;YAC3B;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBACG;;;;;;AAGP;GA1Da;KAAA;AA4DN,MAAM,UAAU;;IACrB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa", "debugId": null}}, {"offset": {"line": 367, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/job-bench/safari-tour-nextjs/src/app/providers.tsx"], "sourcesContent": ["'use client'\n\nimport { QueryClient, QueryClientProvider } from \"@tanstack/react-query\";\nimport { AuthProvider } from \"@/contexts/AuthContext\";\nimport { CartProvider } from \"@/contexts/CartContext\";\nimport { Toaster } from \"sonner\";\nimport { useState } from \"react\";\n\nexport function Providers({ children }: { children: React.ReactNode }) {\n  const [queryClient] = useState(() => new QueryClient({\n    defaultOptions: {\n      queries: {\n        staleTime: 5 * 60 * 1000, // 5 minutes\n        retry: 1,\n      },\n    },\n  }));\n\n  return (\n    <QueryClientProvider client={queryClient}>\n      <AuthProvider>\n        <CartProvider>\n          {children}\n          <Toaster />\n        </CartProvider>\n      </AuthProvider>\n    </QueryClientProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQO,SAAS,UAAU,EAAE,QAAQ,EAAiC;;IACnE,MAAM,CAAC,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;8BAAE,IAAM,IAAI,gLAAA,CAAA,cAAW,CAAC;gBACnD,gBAAgB;oBACd,SAAS;wBACP,WAAW,IAAI,KAAK;wBACpB,OAAO;oBACT;gBACF;YACF;;IAEA,qBACE,6LAAC,yLAAA,CAAA,sBAAmB;QAAC,QAAQ;kBAC3B,cAAA,6LAAC,kIAAA,CAAA,eAAY;sBACX,cAAA,6LAAC,kIAAA,CAAA,eAAY;;oBACV;kCACD,6LAAC,2IAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;AAKlB;GApBgB;KAAA", "debugId": null}}]}